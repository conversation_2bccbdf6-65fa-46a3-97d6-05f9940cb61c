<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Products;
use App\Models\ProductCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Exception;
use Illuminate\Support\Facades\Log;

class ManageProductsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if($request->ajax()){
            $draw = $request->get('draw');
            $start = $request->get('start');
            $length = $request->get('length');
            $searchValue = $request->get('search')['value'] ?? '';
            $orderColumn = $request->get('order')[0]['column'] ?? 0;
            $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

            $columns = ['id', 'name', 'sku', 'status', 'created_at', 'updated_at'];
            $orderColumn = $columns[$orderColumn] ?? 'id';

            // Get total records count (without search filter)
            $totalRecords = Products::count();

            $query = Products::with('category')->select('id', 'name', 'slug', 'sku', 'category_id', 'primary_image', 'status', 'created_at', 'updated_at');

            if ($searchValue) {
                $query->where(function ($q) use ($searchValue) {
                    $q->where('name', 'like', '%' . $searchValue . '%')
                      ->orWhere('sku', 'like', '%' . $searchValue . '%')
                      ->orWhereHas('category', function($categoryQuery) use ($searchValue) {
                          $categoryQuery->where('name', 'like', '%' . $searchValue . '%');
                      });
                });
            }

            // Get filtered records count (with search filter applied)
            $filteredRecords = $query->count();
            $products = $query->skip($start)->take($length)->orderBy($orderColumn, $orderDirection)->get();

            $data = [];
            foreach ($products as $key => $product) {
                $data[] = [
                    'DT_RowIndex' => $start + $key + 1,
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'category' => $product->category ? $product->category->name : 'N/A',
                    'image' => $product->primary_image ? '<img src="' . asset('uploads/products/' . $product->primary_image) . '" alt="Product Image" class="rounded" style="width: 50px; height: 50px; object-fit: cover;">' : '<span class="text-muted">No Image</span>',
                    'status' => $this->getProductStatusBadge($product->status),
                    'created_at' => Helper::formatDate($product->created_at),
                    'updated_at' => Helper::formatDate($product->updated_at),
                    'actions' => Helper::getActionButtons($product->id, 'products', ['show', 'edit', 'delete'])
                ];
            }
            return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
        }
        return view('Web.Products.Products.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = ProductCategory::where('status', 1)->orderBy('name')->get();
        return view('Web.Products.Products.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            // Auto-generate SKU if not provided
            $sku = $request->sku;
            if (empty($sku)) {
                $sku = $this->generateSKU($request->name);
            }

            // Validation rules (SKU is now optional)
            $validator = Validator::make(array_merge($request->all(), ['sku' => $sku]), [
                'category_id' => 'required|exists:product_categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|max:255|unique:products,sku',
                'quantity' => 'required|integer|min:0',
                'cost_price' => 'required|numeric|min:0',
                'sell_price' => 'nullable|numeric|min:0',
                'description' => 'required|string',
                'status' => 'required|in:draft,published,archived',
                'is_featured' => 'nullable|in:0,1',
                'is_tracking' => 'nullable|in:0,1',
                'primary_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Generate unique slug
            $slug = $this->generateSlug($request->name);

            // Handle primary image upload
            $primaryImageName = null;
            if ($request->hasFile('primary_image')) {
                $primaryImageName = $this->uploadImage($request->file('primary_image'));
            }

            // Handle gallery images upload
            $galleryImages = [];
            if ($request->hasFile('gallery_images')) {
                foreach ($request->file('gallery_images') as $image) {
                    $galleryImages[] = $this->uploadImage($image);
                }
            }

            // Create product
            $product = Products::create([
                'category_id' => $request->category_id,
                'name' => $request->name,
                'slug' => $slug,
                'sku' => $sku,
                'quantity' => $request->quantity,
                'primary_image' => $primaryImageName,
                'gallery_images' => !empty($galleryImages) ? json_encode($galleryImages) : null,
                'cost_price' => $request->cost_price,
                'sell_price' => $request->sell_price,
                'is_featured' => $request->input('is_featured') ? 1 : 0,
                'is_tracking' => $request->input('is_tracking') ? 1 : 0,
                'status' => $request->status,
                'description' => $request->description,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product created successfully!',
                'data' => $product
            ]);

        } catch (Exception $e) {
            Log::error('ProductController@store: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while creating the product.'
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $product = Products::with('category')->findOrFail($id);
            return view('Web.Products.Products.show', compact('product'));
        } catch (Exception $e) {
            Log::error('ProductController@show: ' . $e->getMessage());
            return redirect()->route('products.index')->with('error', 'Product not found.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $product = Products::with('category')->findOrFail($id);
            $categories = ProductCategory::where('status', 1)->orderBy('name')->get();
            return view('Web.Products.Products.edit', compact('product', 'categories'));
        } catch (Exception $e) {
            Log::error('ProductController@edit: ' . $e->getMessage());
            return redirect()->route('products.index')->with('error', 'Product not found.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $product = Products::findOrFail($id);

            // Auto-generate SKU if not provided or empty
            $sku = $request->sku;
            if (empty($sku)) {
                $sku = $this->generateSKU($request->name, $id);
            }

            // Validation rules (SKU is now optional in request)
            $validator = Validator::make(array_merge($request->all(), ['sku' => $sku]), [
                'category_id' => 'required|exists:product_categories,id',
                'name' => 'required|string|max:255',
                'sku' => 'required|string|max:255|unique:products,sku,' . $id,
                'quantity' => 'required|integer|min:0',
                'cost_price' => 'required|numeric|min:0',
                'sell_price' => 'nullable|numeric|min:0',
                'description' => 'required|string',
                'status' => 'required|in:draft,published,archived',
                'is_featured' => 'nullable|in:0,1',
                'is_tracking' => 'nullable|in:0,1',
                'primary_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120',
                'gallery_images.*' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Generate slug if name changed
            $slug = $product->slug;
            if ($product->name !== $request->name) {
                $slug = $this->generateSlug($request->name, $id);
            }

            // Handle primary image upload
            $primaryImageName = $product->primary_image;
            if ($request->hasFile('primary_image')) {
                // Delete old image if exists
                if ($product->primary_image) {
                    $this->deleteImage($product->primary_image);
                }
                $primaryImageName = $this->uploadImage($request->file('primary_image'));
            }

            // Handle gallery images upload
            $galleryImages = $product->gallery_images ? json_decode($product->gallery_images, true) : [];
            if ($request->hasFile('gallery_images')) {
                // Delete old gallery images if new ones are uploaded
                if ($product->gallery_images) {
                    $oldGalleryImages = json_decode($product->gallery_images, true);
                    foreach ($oldGalleryImages as $oldImage) {
                        $this->deleteImage($oldImage);
                    }
                }

                $galleryImages = [];
                foreach ($request->file('gallery_images') as $image) {
                    $galleryImages[] = $this->uploadImage($image);
                }
            }

            // Update product
            $product->update([
                'category_id' => $request->category_id,
                'name' => $request->name,
                'slug' => $slug,
                'sku' => $sku,
                'quantity' => $request->quantity,
                'primary_image' => $primaryImageName,
                'gallery_images' => !empty($galleryImages) ? json_encode($galleryImages) : null,
                'cost_price' => $request->cost_price,
                'sell_price' => $request->sell_price,
                'is_featured' => $request->input('is_featured') ? 1 : 0,
                'is_tracking' => $request->input('is_tracking') ? 1 : 0,
                'status' => $request->status,
                'description' => $request->description,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Product updated successfully!',
                'data' => $product
            ]);

        } catch (Exception $e) {
            Log::error('ProductController@update: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating the product.'
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $product = Products::findOrFail($id);

            // Delete associated images
            if ($product->primary_image) {
                $this->deleteImage($product->primary_image);
            }

            if ($product->gallery_images) {
                $galleryImages = json_decode($product->gallery_images, true);
                foreach ($galleryImages as $image) {
                    $this->deleteImage($image);
                }
            }

            // Delete the product
            $product->delete();

            return response()->json([
                'success' => true,
                'message' => 'Product deleted successfully!'
            ]);

        } catch (Exception $e) {
            Log::error('ProductController@destroy: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while deleting the product.'
            ], 500);
        }
    }

    /**
     * Upload image to products directory
     */
    private function uploadImage($image)
    {
        $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();
        $image->move(public_path('uploads/products'), $imageName);
        return $imageName;
    }

    /**
     * Delete image from products directory
     */
    private function deleteImage($imageName)
    {
        $imagePath = public_path('uploads/products/' . $imageName);
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
    }

    /**
     * Get product status badge
     */
    private function getProductStatusBadge($status)
    {
        switch ($status) {
            case 'published':
                return '<span class="badge badge-success">Published</span>';
            case 'draft':
                return '<span class="badge badge-warning">Draft</span>';
            case 'archived':
                return '<span class="badge badge-secondary">Archived</span>';
            default:
                return '<span class="badge badge-secondary">Unknown</span>';
        }
    }

    /**
     * Generate unique slug from product name
     */
    private function generateSlug($name, $excludeId = null)
    {
        $slug = Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        $query = Products::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        while ($query->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;

            $query = Products::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $slug;
    }

    /**
     * Generate unique SKU from product name
     */
    private function generateSKU($name, $excludeId = null)
    {
        // Create base SKU from product name
        $baseSku = strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));

        // Limit to first 6 characters and add random suffix
        $baseSku = substr($baseSku, 0, 6);

        // If base SKU is too short, pad with random characters
        if (strlen($baseSku) < 3) {
            $baseSku = $baseSku . strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 3 - strlen($baseSku)));
        }

        // Generate unique SKU
        $sku = $baseSku . '-' . strtoupper(substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 4));

        $query = Products::where('sku', $sku);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        // If SKU exists, generate a new one with timestamp
        while ($query->exists()) {
            $sku = $baseSku . '-' . strtoupper(substr(str_shuffle('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 4));

            $query = Products::where('sku', $sku);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $sku;
    }
}
