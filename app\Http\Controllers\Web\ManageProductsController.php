<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\Products;
use Illuminate\Http\Request;
use Termwind\Components\Raw;

class ManageProductsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if($request->ajax()){
            $draw = $request->get('draw');
            $start = $request->get('start');
            $length = $request->get('length');
            $searchValue = $request->get('search')['value'] ?? '';
            $orderColumn = $request->get('order')[0]['column'] ?? 0;
            $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

            $columns = ['id', 'name', 'status', 'created_at', 'updated_at'];
            $orderColumn = $columns[$orderColumn] ?? 'id';

            // Get total records count (without search filter)
            $totalRecords = Products::count();

            $query = Products::select('id', 'name', 'slug', 'status', 'created_at', 'updated_at');

            if ($searchValue) {
                $query->where(function ($query) use ($searchValue) {
                    $query->where('name', 'like', '%' . $searchValue . '%')->orWhere('slug', 'like', '%' . $searchValue . '%');
                });
            }

            // Get filtered records count (with search filter applied)
            $filteredRecords = $query->count();
            $products = $query->skip($start)->take($length)->orderBy($orderColumn, $orderDirection)->get();

            $data = [];
            foreach ($products as $key => $product) {
                $data[] = [
                    'DT_RowIndex' => $start + $key + 1,
                    'id' => $product->id,
                    'name' => $product->name,
                    'status' => Helper::getStatusBadge($product->status),
                    'created_at' => Helper::formatDate($product->created_at),
                    'updated_at' => Helper::formatDate($product->updated_at),
                    'actions' => Helper::getActionButtons($product->id, 'products', ['edit', 'delete'])
                ];
            }
            return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
        }
        return view('Web.Products.Products.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.Products.Products.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
