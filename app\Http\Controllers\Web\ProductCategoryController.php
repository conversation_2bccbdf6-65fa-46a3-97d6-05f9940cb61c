<?php

namespace App\Http\Controllers\Web;

use App\Helpers\Helper;
use App\Http\Controllers\Controller;
use App\Models\ProductCategory;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProductCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            try {
                $draw = $request->get('draw');
                $start = $request->get('start');
                $length = $request->get('length');
                $searchValue = $request->get('search')['value'] ?? '';
                $orderColumn = $request->get('order')[0]['column'] ?? 0;
                $orderDirection = $request->get('order')[0]['dir'] ?? 'asc';

                $columns = ['id', 'name', 'status', 'created_at', 'updated_at'];
                $orderColumn = $columns[$orderColumn] ?? 'id';

                // Get total records count (without search filter)
                $totalRecords = ProductCategory::count();

                $query = ProductCategory::select('id', 'name', 'slug', 'status', 'created_at', 'updated_at');

                if ($searchValue) {
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('name', 'like', '%' . $searchValue . '%')->orWhere('slug', 'like', '%' . $searchValue . '%');
                    });
                }

                // Get filtered records count (with search filter applied)
                $filteredRecords = $query->count();
                $productCategories = $query->skip($start)->take($length)->orderBy($orderColumn, $orderDirection)->get();

                $data = [];
                foreach ($productCategories as $key => $productCategory) {
                    $data[] = [
                        'DT_RowIndex' => $start + $key + 1,
                        'id' => $productCategory->id,
                        'name' => $productCategory->name,
                        'status' => Helper::getStatusBadge($productCategory->status),
                        'created_at' => Helper::formatDate($productCategory->created_at),
                        'updated_at' => Helper::formatDate($productCategory->updated_at),
                        'actions' => Helper::getActionButtons($productCategory->id, 'product-categories', ['edit', 'delete'])
                    ];
                }
                return response()->json(['draw' => intval($draw), 'recordsTotal' => $totalRecords, 'recordsFiltered' => $filteredRecords, 'data' => $data]);
            } catch (Exception $e) {
                Log::error('ProductCategoryController@index: ' . $e->getMessage());
                return response()->json(['error' => 'An error occurred while fetching data.'], 500);
            }
        }
        return view('Web.Products.Product-Category.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Web.Products.Product-Category.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name' => 'required|string|max:255|unique:product_categories,name',
                'status' => 'required|boolean',
            ]);

            $productCategory = ProductCategory::create([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'status' => $request->status,
            ]);

            return Helper::successResponse('Product category created successfully!', $productCategory);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['success' => false, 'errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('ProductCategoryController@store: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while creating the product category.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        try {
            $productCategory = ProductCategory::findOrFail($id);
            return view('Web.Products.Product-Category.show', compact('productCategory'));
        } catch (Exception $e) {
            Log::error('ProductCategoryController@show: ' . $e->getMessage());
            return redirect()->route('product-categories.index')->with('error', 'Product category not found.');
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        try {
            $productCategory = ProductCategory::findOrFail($id);
            return view('Web.Products.Product-Category.edit', compact('productCategory'));
        } catch (Exception $e) {
            Log::error('ProductCategoryController@edit: ' . $e->getMessage());
            return redirect()->route('product-categories.index')->with('error', 'Product category not found.');
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        try {
            $productCategory = ProductCategory::findOrFail($id);

            $request->validate([
                'name' => 'required|string|max:255|unique:product_categories,name,' . $id,
                'status' => 'required|boolean',
            ]);

            $productCategory->update([
                'name' => $request->name,
                'slug' => Str::slug($request->name),
                'status' => $request->status,
            ]);

            return Helper::successResponse('Product category updated successfully!', $productCategory);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json(['success' => false, 'errors' => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error('ProductCategoryController@update: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while updating the product category.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $productCategory = ProductCategory::findOrFail($id);
            $productCategory->delete();

            return Helper::successResponse('Product category deleted successfully!');
        } catch (Exception $e) {
            Log::error('ProductCategoryController@destroy: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while deleting the product category.'], 500);
        }
    }
}
