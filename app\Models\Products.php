<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Products extends Model
{
    use SoftDeletes;
    protected $table = 'products';
    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'sku',
        'hsn_code',
        'quantity',
        'unit_of_measurement',
        'weight',
        'dimensions',
        'primary_image',
        'gallery_images',
        'cost_price',
        'sell_price',
        'gst_rate',
        'cgst_rate',
        'sgst_rate',
        'igst_rate',
        'cess_rate',
        'base_price',
        'tax_amount',
        'mrp',
        'discount_percentage',
        'discount_amount',
        'is_taxable',
        'tax_type',
        'is_featured',
        'is_tracking',
        'status',
        'description',
    ];

    protected $casts = [
        'dimensions' => 'array',
        'is_featured' => 'boolean',
        'is_tracking' => 'boolean',
        'is_taxable' => 'boolean',
    ];

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Calculate GST amount based on base price
     */
    public function getGstAmountAttribute()
    {
        if (!$this->is_taxable || !$this->base_price) {
            return 0;
        }
        return ($this->base_price * $this->gst_rate) / 100;
    }

    /**
     * Calculate CGST amount
     */
    public function getCgstAmountAttribute()
    {
        if (!$this->is_taxable || !$this->base_price) {
            return 0;
        }
        return ($this->base_price * $this->cgst_rate) / 100;
    }

    /**
     * Calculate SGST amount
     */
    public function getSgstAmountAttribute()
    {
        if (!$this->is_taxable || !$this->base_price) {
            return 0;
        }
        return ($this->base_price * $this->sgst_rate) / 100;
    }

    /**
     * Calculate IGST amount
     */
    public function getIgstAmountAttribute()
    {
        if (!$this->is_taxable || !$this->base_price) {
            return 0;
        }
        return ($this->base_price * $this->igst_rate) / 100;
    }

    /**
     * Calculate Cess amount
     */
    public function getCessAmountAttribute()
    {
        if (!$this->is_taxable || !$this->base_price) {
            return 0;
        }
        return ($this->base_price * $this->cess_rate) / 100;
    }

    /**
     * Calculate total tax amount
     */
    public function getTotalTaxAmountAttribute()
    {
        if (!$this->is_taxable) {
            return 0;
        }
        return $this->gst_amount + $this->cess_amount;
    }

    /**
     * Calculate final selling price including tax
     */
    public function getFinalPriceAttribute()
    {
        if (!$this->base_price) {
            return $this->sell_price ?? 0;
        }

        if ($this->tax_type === 'inclusive') {
            return $this->base_price;
        } else {
            return $this->base_price + $this->total_tax_amount;
        }
    }

    /**
     * Calculate profit margin
     */
    public function getProfitMarginAttribute()
    {
        if (!$this->cost_price || !$this->final_price) {
            return 0;
        }
        $profit = $this->final_price - $this->cost_price;
        return ($profit / $this->final_price) * 100;
    }

    /**
     * Format price in Indian Rupees
     */
    public function formatPrice($amount)
    {
        return '₹' . number_format($amount, 2);
    }

    /**
     * Get formatted cost price
     */
    public function getFormattedCostPriceAttribute()
    {
        return $this->formatPrice($this->cost_price);
    }

    /**
     * Get formatted selling price
     */
    public function getFormattedSellPriceAttribute()
    {
        return $this->formatPrice($this->sell_price ?? 0);
    }

    /**
     * Get formatted final price
     */
    public function getFormattedFinalPriceAttribute()
    {
        return $this->formatPrice($this->final_price);
    }

    /**
     * Get formatted MRP
     */
    public function getFormattedMrpAttribute()
    {
        return $this->formatPrice($this->mrp ?? 0);
    }
}
