<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Products extends Model
{
    use SoftDeletes;
    protected $table = 'products';
    protected $fillable = [
        'category_id',
        'name',
        'slug',
        'sku',
        'quantity',
        'primary_image',
        'gallery_images',
        'cost_price',
        'sell_price',
        'is_featured',
        'is_tracking',
        'status',
        'description',
    ];

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }
}
