<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();

            // Product Category
            $table->foreignId('category_id')->constrained('product_categories')->onDelete('cascade');

            // Product Details
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('sku')->unique();
            $table->integer('quantity');

            // Product Images
            $table->string('primary_image')->nullable();
            $table->json('gallery_images')->nullable();

            // Product Pricing
            $table->decimal('cost_price', 10, 2);
            $table->decimal('sell_price', 10, 2)->nullable();

            // Product Tracking and Featred Product Checkboxes
            $table->boolean('is_featured')->default(0);
            $table->boolean('is_tracking')->default(1);

            // Product Status and Descriptions
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->text('description');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
