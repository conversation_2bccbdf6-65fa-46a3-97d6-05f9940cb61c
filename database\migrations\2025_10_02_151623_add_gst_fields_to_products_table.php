<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            // GST and Tax Information
            $table->string('hsn_code', 20)->nullable()->after('sku'); // HSN/SAC Code
            $table->decimal('gst_rate', 5, 2)->default(18.00)->after('sell_price'); // GST Rate (0-28%)
            $table->decimal('cgst_rate', 5, 2)->default(9.00)->after('gst_rate'); // CGST Rate
            $table->decimal('sgst_rate', 5, 2)->default(9.00)->after('cgst_rate'); // SGST Rate
            $table->decimal('igst_rate', 5, 2)->default(18.00)->after('sgst_rate'); // IGST Rate
            $table->decimal('cess_rate', 5, 2)->default(0.00)->after('igst_rate'); // Cess Rate

            // Pricing with Tax Calculations
            $table->decimal('base_price', 10, 2)->nullable()->after('cess_rate'); // Price before tax
            $table->decimal('tax_amount', 10, 2)->nullable()->after('base_price'); // Total tax amount
            $table->decimal('mrp', 10, 2)->nullable()->after('tax_amount'); // Maximum Retail Price
            $table->decimal('discount_percentage', 5, 2)->default(0.00)->after('mrp'); // Discount %
            $table->decimal('discount_amount', 10, 2)->default(0.00)->after('discount_percentage'); // Discount amount

            // Additional Indian Business Fields
            $table->string('unit_of_measurement', 20)->default('PCS')->after('quantity'); // Unit (PCS, KG, LITER, etc.)
            $table->decimal('weight', 8, 3)->nullable()->after('unit_of_measurement'); // Weight in KG
            $table->json('dimensions')->nullable()->after('weight'); // L x W x H in cm
            $table->boolean('is_taxable')->default(true)->after('dimensions'); // Is product taxable
            $table->enum('tax_type', ['inclusive', 'exclusive'])->default('exclusive')->after('is_taxable'); // Tax inclusive/exclusive
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'hsn_code',
                'gst_rate',
                'cgst_rate',
                'sgst_rate',
                'igst_rate',
                'cess_rate',
                'base_price',
                'tax_amount',
                'mrp',
                'discount_percentage',
                'discount_amount',
                'unit_of_measurement',
                'weight',
                'dimensions',
                'is_taxable',
                'tax_type'
            ]);
        });
    }
};
