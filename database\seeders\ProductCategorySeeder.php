<?php

namespace Database\Seeders;

use App\Models\ProductCategory;
use Illuminate\Database\Seeder;

class ProductCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            ['name' => 'Electronics', 'slug' => 'electronics', 'status' => 1],
            ['name' => 'Clothing', 'slug' => 'clothing', 'status' => 1],
            ['name' => 'Books', 'slug' => 'books', 'status' => 0],
            ['name' => 'Home & Garden', 'slug' => 'home-garden', 'status' => 1],
            ['name' => 'Sports', 'slug' => 'sports', 'status' => 1],
        ];

        foreach ($categories as $category) {
            ProductCategory::create($category);
        }
    }
}
