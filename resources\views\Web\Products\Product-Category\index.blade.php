@extends('Layouts.app')

@section('title', 'Arklok Admin | Product Categories')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Product Categories Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize product categories</span></h1></div>
                            <div><a href="{{ route('product-categories.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Product Category</a></div>
                        </div>
                    </div>
                </div>

                <!-- Table Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive" style="overflow-x: auto;">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5 nowrap" id="product-category-table" style="width: 100%;">
                                        <thead>
                                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">ID</th>
                                                <th class="min-w-100px">Name</th>
                                                <th class="min-w-100px">Status</th>
                                                <th class="min-w-100px">Created At</th>
                                                <th class="min-w-100px">Updated At</th>
                                                <th class="min-w-100px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            const columns = [
                getColumnDefinition('index'),
                { data: 'name', name: 'name' },
                getColumnDefinition('status'),
                getColumnDefinition('date', { data: 'created_at', name: 'created_at' }),
                getColumnDefinition('date', { data: 'updated_at', name: 'updated_at' }),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'product-category-table',
                ajaxUrl: '{{ route("product-categories.index") }}',
                columns: columns,
                itemName: 'product category',
                order: [[0, 'desc']], // Order by created_at desc
            });
            console.log('Table initialized:', table);
            // Delete functionality using universal function
            $(document).on('click', '.delete-product-category', function() {
                const productCategoryId = $(this).data('id');
                universalDelete({
                    id: productCategoryId,
                    url: '{{ route("product-categories.destroy", ":id") }}',
                    itemName: 'product category',
                    table: table
                });
            });
        });
    </script>
@endsection
