@extends('Layouts.app')

@section('title', 'Edit Product')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Edit Product: {{ $product->name }}
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">Update product information</span>
                                </h1>
                            </div>
                            <div>
                                <a href="{{ route('products.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Products
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                                <div class="card-title">
                                    <h3 class="card-title align-items-start flex-column">
                                        <span class="card-label fw-bold fs-3 mb-1">Product Information</span>
                                        <span class="text-muted fw-semibold fs-7">Update the details below</span>
                                    </h3>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <form id="product-form" action="{{ route('products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT')

                                    <div class="row">
                                        <!-- Left Column -->
                                        <div class="col-lg-8">
                                            <!-- Basic Information -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Basic Information</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Product Name</label>
                                                        <div class="col-lg-8">
                                                            <input type="text" name="name" class="form-control form-control-lg form-control-solid" placeholder="Enter product name" value="{{ old('name', $product->name) }}" required>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">SKU</label>
                                                        <div class="col-lg-8">
                                                            <input type="text" name="sku" class="form-control form-control-lg form-control-solid" placeholder="Enter product SKU" value="{{ old('sku', $product->sku) }}" required>
                                                            <div class="form-text">Unique product identifier</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Category</label>
                                                        <div class="col-lg-8">
                                                            <select name="category_id" class="form-select form-select-lg form-select-solid" required>
                                                                <option value="">Select Category</option>
                                                                @foreach($categories as $category)
                                                                    <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                                                        {{ $category->name }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Description</label>
                                                        <div class="col-lg-8">
                                                            <textarea name="description" class="form-control form-control-lg form-control-solid" rows="4" placeholder="Enter product description" required>{{ old('description', $product->description) }}</textarea>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Pricing & Inventory -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Pricing & Inventory</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Cost Price</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">$</span>
                                                                <input type="number" name="cost_price" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0" value="{{ old('cost_price', $product->cost_price) }}" required>
                                                            </div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Selling Price</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">$</span>
                                                                <input type="number" name="sell_price" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0" value="{{ old('sell_price', $product->sell_price) }}">
                                                            </div>
                                                            <div class="form-text">Leave empty if not for sale</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Quantity</label>
                                                        <div class="col-lg-8">
                                                            <input type="number" name="quantity" class="form-control form-control-lg form-control-solid" placeholder="0" min="0" value="{{ old('quantity', $product->quantity) }}" required>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="col-lg-4">
                                            <!-- Current Images -->
                                            @if($product->primary_image || $product->gallery_images)
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Current Images</h3>
                                                </div>
                                                <div class="card-body">
                                                    @if($product->primary_image)
                                                    <div class="mb-4">
                                                        <label class="form-label fw-semibold fs-6">Primary Image</label>
                                                        <div class="current-primary-image">
                                                            <img src="{{ asset('uploads/products/' . $product->primary_image) }}" alt="Primary Image" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                                        </div>
                                                    </div>
                                                    @endif

                                                    @if($product->gallery_images)
                                                    <div class="mb-4">
                                                        <label class="form-label fw-semibold fs-6">Gallery Images</label>
                                                        <div class="current-gallery-images">
                                                            @foreach(json_decode($product->gallery_images, true) as $image)
                                                            <div class="d-inline-block me-2 mb-2">
                                                                <img src="{{ asset('uploads/products/' . $image) }}" alt="Gallery Image" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                                            </div>
                                                            @endforeach
                                                        </div>
                                                    </div>
                                                    @endif
                                                </div>
                                            </div>
                                            @endif

                                            <!-- Update Images -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Update Images</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-6">
                                                        <label class="form-label fw-semibold fs-6">Primary Image</label>
                                                        <input type="file" name="primary_image" class="form-control form-control-lg form-control-solid" accept="image/*">
                                                        <div class="form-text">Upload new primary image to replace current one (JPEG, PNG, JPG, GIF - Max: 2MB)</div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <label class="form-label fw-semibold fs-6">Gallery Images</label>
                                                        <input type="file" name="gallery_images[]" class="form-control form-control-lg form-control-solid" accept="image/*" multiple>
                                                        <div class="form-text">Upload new gallery images to replace current ones (Multiple files allowed)</div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Product Settings -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Product Settings</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-6">
                                                        <label class="form-label required fw-semibold fs-6">Status</label>
                                                        <select name="status" class="form-select form-select-lg form-select-solid" required>
                                                            <option value="draft" {{ old('status', $product->status) == 'draft' ? 'selected' : '' }}>Draft</option>
                                                            <option value="published" {{ old('status', $product->status) == 'published' ? 'selected' : '' }}>Published</option>
                                                            <option value="archived" {{ old('status', $product->status) == 'archived' ? 'selected' : '' }}>Archived</option>
                                                        </select>
                                                        <div class="invalid-feedback"></div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured" {{ old('is_featured', $product->is_featured) ? 'checked' : '' }}>
                                                            <label class="form-check-label fw-semibold fs-6" for="is_featured">
                                                                Featured Product
                                                            </label>
                                                        </div>
                                                        <div class="form-text">Mark as featured product</div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" name="is_tracking" id="is_tracking" {{ old('is_tracking', $product->is_tracking) ? 'checked' : '' }}>
                                                            <label class="form-check-label fw-semibold fs-6" for="is_tracking">
                                                                Track Inventory
                                                            </label>
                                                        </div>
                                                        <div class="form-text">Enable inventory tracking</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="d-flex justify-content-end">
                                        <a href="{{ route('products.index') }}" class="btn btn-light me-3">Cancel</a>
                                        <button type="submit" id="submit-btn" class="btn btn-primary">
                                            <span class="indicator-label">Update Product</span>
                                            <span class="indicator-progress">Please wait...
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with AJAX submission
            initializeEnhancedForm({
                formId: 'product-form',
                submitBtnId: 'submit-btn',
                successMessage: 'Product has been updated successfully!',
                redirectUrl: '{{ route("products.index") }}',
                hasFileUpload: true,
                enableCKEditor: false,
                exitSelector: 'a[href="{{ route("products.index") }}"]'
            });

            // Image preview functionality for new uploads
            $('input[name="primary_image"]').on('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Remove existing preview
                        $(this).closest('.mb-6').find('.new-image-preview').remove();

                        // Add new preview
                        const preview = `
                            <div class="new-image-preview mt-3">
                                <img src="${e.target.result}" alt="New Primary Image Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                <div class="mt-2">
                                    <small class="text-success">New Primary Image Preview</small>
                                </div>
                            </div>
                        `;
                        $(this).closest('.mb-6').append(preview);
                    }.bind(this);
                    reader.readAsDataURL(file);
                }
            });

            // Gallery images preview for new uploads
            $('input[name="gallery_images[]"]').on('change', function() {
                const files = this.files;
                const container = $(this).closest('.mb-6');

                // Remove existing previews
                container.find('.new-gallery-preview').remove();

                if (files.length > 0) {
                    const previewContainer = $('<div class="new-gallery-preview mt-3"></div>');
                    previewContainer.append('<small class="text-success d-block mb-2">New Gallery Images Preview:</small>');

                    Array.from(files).forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const preview = `
                                <div class="d-inline-block me-2 mb-2">
                                    <img src="${e.target.result}" alt="New Gallery Image ${index + 1}" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                    <div class="text-center">
                                        <small class="text-muted">Image ${index + 1}</small>
                                    </div>
                                </div>
                            `;
                            previewContainer.append(preview);
                        };
                        reader.readAsDataURL(file);
                    });

                    container.append(previewContainer);
                }
            });
        });
    </script>
@endsection