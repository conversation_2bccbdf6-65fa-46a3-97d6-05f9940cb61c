@extends('Layouts.app')

@section('title', 'Arklok Admin | Products')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div><h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Products Management<span class="page-desc text-muted fs-7 fw-semibold pt-1">Manage and organize products</span></h1></div>
                            <div><a href="{{ route('products.create') }}" class="btn btn-primary"><i class="fas fa-plus"></i> Add New Product</a></div>
                        </div>
                    </div>
                </div>

                <!-- Table Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-body pt-0">
                                <div class="table-responsive" style="overflow-x: auto;">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5 nowrap" id="product-table" style="width: 100%;">
                                        <thead>
                                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                <th class="min-w-50px">ID</th>
                                                <th class="min-w-100px">Image</th>
                                                <th class="min-w-150px">Name</th>
                                                <th class="min-w-100px">Category</th>
                                                <th class="min-w-80px">Final Price</th>
                                                <th class="min-w-60px">GST</th>
                                                <th class="min-w-80px">MRP</th>
                                                <th class="min-w-100px">Status</th>
                                                <th class="min-w-150px">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="text-gray-600 fw-semibold">
                                            <!-- Data will be loaded via AJAX -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        $(document).ready(function() {
            const columns = [
                getColumnDefinition('index'),
                { data: 'image', name: 'image', orderable: false, searchable: false },
                { data: 'name', name: 'name' },
                { data: 'category', name: 'category' },
                { data: 'final_price', name: 'final_price', className: 'text-end fw-bold text-success' },
                { data: 'gst_rate', name: 'gst_rate', className: 'text-center' },
                { data: 'mrp', name: 'mrp', className: 'text-end' },
                getColumnDefinition('status'),
                getColumnDefinition('actions')
            ];

            // Initialize DataTable using helper function
            let table = initializeDataTable({
                tableId: 'product-table',
                ajaxUrl: '{{ route("products.index") }}',
                columns: columns,
                itemName: 'product',
                responsive: false,
                scrollX: true,
                order: [[0, 'desc']] // Order by created_at desc
            });

            // Delete functionality using universal function
            $(document).on('click', '.delete-product', function() {
                const productId = $(this).data('id');
                universalDelete({
                    id: productId,
                    url: '{{ route("products.destroy", ":id") }}',
                    itemName: 'product',
                    table: table
                });
            });
        });
    </script>
@endsection
