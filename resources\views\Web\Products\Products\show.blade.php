@extends('Layouts.app')

@section('title', 'Product Details')

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Product Details: {{ $product->name }}
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">View complete product information</span>
                                </h1>
                            </div>
                            <div>
                                <a href="{{ route('products.index') }}" class="btn btn-secondary me-2">
                                    <i class="fas fa-arrow-left"></i> Back to Products
                                </a>
                                <a href="{{ route('products.edit', $product->id) }}" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Product
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-5 g-xl-10">
                    <!-- Left Column - Product Images -->
                    <div class="col-lg-5">
                        <div class="card card-flush">
                            <div class="card-header">
                                <h3 class="card-title">Product Images</h3>
                            </div>
                            <div class="card-body">
                                @if($product->primary_image)
                                    <div class="mb-5">
                                        <label class="form-label fw-semibold fs-6 mb-3">Primary Image</label>
                                        <div class="text-center">
                                            <img src="{{ asset('uploads/products/' . $product->primary_image) }}"
                                                 alt="{{ $product->name }}"
                                                 class="img-fluid rounded shadow-sm"
                                                 style="max-height: 400px; cursor: pointer;"
                                                 onclick="openImageModal(this.src)">
                                        </div>
                                    </div>
                                @endif

                                @if($product->gallery_images)
                                    @php
                                        $galleryImages = json_decode($product->gallery_images, true);
                                    @endphp
                                    @if(!empty($galleryImages))
                                        <div class="mb-5">
                                            <label class="form-label fw-semibold fs-6 mb-3">Gallery Images</label>
                                            <div class="row g-3">
                                                @foreach($galleryImages as $image)
                                                    <div class="col-4">
                                                        <img src="{{ asset('uploads/products/' . $image) }}"
                                                             alt="Gallery Image"
                                                             class="img-thumbnail w-100"
                                                             style="height: 120px; object-fit: cover; cursor: pointer;"
                                                             onclick="openImageModal(this.src)">
                                                    </div>
                                                @endforeach
                                            </div>
                                        </div>
                                    @endif
                                @endif

                                @if(!$product->primary_image && !$product->gallery_images)
                                    <div class="text-center py-5">
                                        <i class="fas fa-image text-muted" style="font-size: 4rem;"></i>
                                        <p class="text-muted mt-3">No images available for this product</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Product Details -->
                    <div class="col-lg-7">
                        <!-- Basic Information -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Basic Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Product Name:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold">{{ $product->name }}</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">SKU:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge badge-light-primary">{{ $product->sku }}</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Category:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge badge-light-info">{{ $product->category ? $product->category->name : 'N/A' }}</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Status:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        @switch($product->status)
                                            @case('published')
                                                <span class="badge badge-success">Published</span>
                                                @break
                                            @case('draft')
                                                <span class="badge badge-warning">Draft</span>
                                                @break
                                            @case('archived')
                                                <span class="badge badge-secondary">Archived</span>
                                                @break
                                            @default
                                                <span class="badge badge-secondary">Unknown</span>
                                        @endswitch
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Description:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="text-gray-800">{{ $product->description }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing & Inventory -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Pricing & Inventory</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Cost Price:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold text-success">${{ number_format($product->cost_price, 2) }}</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Selling Price:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        @if($product->sell_price)
                                            <span class="fw-bold text-primary">${{ number_format($product->sell_price, 2) }}</span>
                                        @else
                                            <span class="text-muted">Not for sale</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Quantity:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold {{ $product->quantity > 0 ? 'text-success' : 'text-danger' }}">
                                            {{ $product->quantity }} units
                                        </span>
                                        @if($product->quantity <= 5 && $product->quantity > 0)
                                            <span class="badge badge-warning ms-2">Low Stock</span>
                                        @elseif($product->quantity == 0)
                                            <span class="badge badge-danger ms-2">Out of Stock</span>
                                        @endif
                                    </div>
                                </div>

                                @if($product->sell_price && $product->cost_price)
                                    @php
                                        $profit = $product->sell_price - $product->cost_price;
                                        $profitMargin = ($profit / $product->sell_price) * 100;
                                    @endphp
                                    <div class="row mb-4">
                                        <div class="col-sm-4">
                                            <label class="fw-semibold text-muted">Profit Margin:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="fw-bold {{ $profit > 0 ? 'text-success' : 'text-danger' }}">
                                                ${{ number_format($profit, 2) }} ({{ number_format($profitMargin, 1) }}%)
                                            </span>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Product Settings -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Product Settings</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Featured Product:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        @if($product->is_featured)
                                            <span class="badge badge-success"><i class="fas fa-star"></i> Yes</span>
                                        @else
                                            <span class="badge badge-light-secondary">No</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Inventory Tracking:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        @if($product->is_tracking)
                                            <span class="badge badge-success"><i class="fas fa-check"></i> Enabled</span>
                                        @else
                                            <span class="badge badge-light-secondary">Disabled</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Created:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted">{{ $product->created_at->format('M d, Y \a\t h:i A') }}</span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Last Updated:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted">{{ $product->updated_at->format('M d, Y \a\t h:i A') }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Product Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Product Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        function openImageModal(imageSrc) {
            $('#modalImage').attr('src', imageSrc);
            $('#imageModal').modal('show');
        }

        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
@endsection
