<?php

use App\Http\Controllers\Web\ProductCategoryController;
use App\Http\Controllers\Web\AuthController;
use App\Http\Controllers\Web\ManageProductsController;
use Illuminate\Support\Facades\Route;

// Login Routes
Route::get('/', [AuthController::class, 'showLoginForm'])->name('showloginform');
Route::post('/login', [AuthController::class, 'login'])->name('login');

// Privacy Policy Routes, Support Routes
Route::get('/privacy-policy', function () {return view('Rules.privacy-policy');})->name('privacy-policy');
Route::get('/support', function () {return view('Rules.support');})->name('support');

Route::group(['middleware' => ['admin.auth']], function () {
    // Page Under Construction Routes
    Route::get('/page-under-construction', function () {return view('Layouts.message');})->name('page-under-construction');

    // Dashboard and Logout Routes
    Route::get('/logout', [AuthController::class, 'logout'])->name('logout');
    Route::get('/admin-dashboard', [AuthController::class, 'showDashboard'])->name('admin-dashboard');

    // Product Routes
    Route::resource('product-categories', ProductCategoryController::class);
    Route::resource('products', ManageProductsController::class);
});
