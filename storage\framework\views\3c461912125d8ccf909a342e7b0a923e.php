<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Arclok Admin | A full-service admin panel'); ?></title>
    <link rel="shortcut icon" href="<?php echo e(asset('media/logos/logo_white.png')); ?>"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />

    
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css">

    
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    
    <link href="<?php echo e(asset('plugins/global/plugins.bundle.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/style.bundle.css')); ?>" rel="stylesheet" type="text/css" />

    
    <link href="<?php echo e(asset('css/datatables.css')); ?>" rel="stylesheet" type="text/css" />

    
    <style>
        /* Select2 Bootstrap 5 theme customization */
        .select2-container--bootstrap-5 .select2-selection--single {
            height: calc(1.5em + 0.75rem + 2px) !important;
            padding: 0.375rem 0.75rem !important;
            font-size: 1rem !important;
            border: 1px solid #d1d3e0 !important;
            border-radius: 0.35rem !important;
            background-color: #f8f9fa !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            padding-left: 0 !important;
            padding-right: 20px !important;
            color: #5a5c69 !important;
        }

        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: calc(1.5em + 0.75rem) !important;
            right: 3px !important;
        }

        .select2-dropdown {
            border: 1px solid #d1d3e0 !important;
            border-radius: 0.35rem !important;
        }

        .select2-container--bootstrap-5.select2-container--focus .select2-selection {
            border-color: #80bdff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        .select2-search--dropdown .select2-search__field {
            border: 1px solid #d1d3e0 !important;
            border-radius: 0.25rem !important;
            padding: 0.375rem 0.75rem !important;
        }

        .select2-results__option--highlighted {
            background-color: #007bff !important;
            color: white !important;
        }
    </style>

    
    <style>
        /* Make the body and html take full height */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        /* Main app container should take full height */
        #kt_app_root {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Page container should flex to fill available space */
        #kt_app_page {
            flex: 1;
            display: flex;
            flex-direction: row;
            overflow: hidden; /* Prevent page-level scrolling */
        }

        /* Wrapper should flex to fill available space */
        #kt_app_wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Content area should be scrollable with padding for fixed footer */
        #kt_app_content {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 40px; /* Space for fixed footer - adjust based on footer height */
            min-height: 0; /* Important for flex scrolling */
        }

        /* Footer should be fixed at bottom of content area only */
        #kt_app_footer {
            position: fixed;
            bottom: 0;
            left: 265px !important; /* Start after sidebar - adjust if your sidebar width is different */
            right: 0;
            z-index: 1000;
            background-color: #fff; /* Ensure footer has background */
            border-top: 1px solid #e4e6ef; /* Optional: add border for separation */
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* Optional: add shadow for depth */
        }

        /* Ensure sidebar doesn't interfere with flex layout */
        .app-sidebar {
            flex-shrink: 0;
        }

        /* Responsive adjustments for mobile - full width when sidebar is collapsed */
        @media (max-width: 991.98px) {
            #kt_app_footer {
                left: 0; /* Full width on mobile when sidebar is hidden */
            }
        }

        /* When sidebar is collapsed/hidden, footer should extend to full width */
        body:not(.app-sidebar-enabled) #kt_app_footer,
        body.app-sidebar-minimize #kt_app_footer {
            left: 0;
        }

        /* Dark theme support */
        [data-theme="dark"] #kt_app_footer {
            background-color: #1e1e2d;
            border-top-color: #2b2b40;
        }
    </style>

    <?php echo $__env->yieldContent('styles'); ?>
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-theme-mode");
            } else {
                if (localStorage.getItem("data-theme") !== null) {
                    themeMode = localStorage.getItem("data-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-theme", themeMode);
        }
    </script>
</head>
<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true" data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <div class="page d-flex flex-row app-page" id="kt_app_page">
            <?php echo $__env->make('Layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            <div class="wrapper d-flex flex-column app-wrapper" id="kt_app_wrapper">
                <?php echo $__env->make('Layouts.navbar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <div class="content d-flex flex-column app-content" id="kt_app_content">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
                <?php echo $__env->make('Layouts.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Layouts/app.blade.php ENDPATH**/ ?>