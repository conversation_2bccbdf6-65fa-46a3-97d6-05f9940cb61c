

<?php $__env->startSection('title', 'Create Product'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Create New Product
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">Add a new product to your inventory</span>
                                </h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Products
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                                <div class="card-title">
                                    <h3 class="card-title align-items-start flex-column">
                                        <span class="card-label fw-bold fs-3 mb-1">Product Information</span>
                                        <span class="text-muted fw-semibold fs-7">Fill in the details below</span>
                                    </h3>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <form id="product-form" action="<?php echo e(route('products.store')); ?>" method="POST" enctype="multipart/form-data">
                                    <?php echo csrf_field(); ?>

                                    <div class="row">
                                        <!-- Left Column -->
                                        <div class="col-lg-8">
                                            <!-- Basic Information -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Basic Information</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Product Name</label>
                                                        <div class="col-lg-8">
                                                            <input type="text" name="name" class="form-control form-control-lg form-control-solid" placeholder="Enter product name" required>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">SKU</label>
                                                        <div class="col-lg-8">
                                                            <input type="text" name="sku" class="form-control form-control-lg form-control-solid" placeholder="Auto-generated if empty">
                                                            <div class="form-text">Unique product identifier (auto-generated if not provided)</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Category</label>
                                                        <div class="col-lg-8">
                                                            <select name="category_id" class="form-select form-select-lg form-select-solid" required>
                                                                <option value="">Select Category</option>
                                                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Description</label>
                                                        <div class="col-lg-8">
                                                            <textarea name="description" class="form-control form-control-lg form-control-solid" rows="4" placeholder="Enter product description" required></textarea>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Pricing & Inventory -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Pricing & Inventory</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Cost Price</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">₹</span>
                                                                <input type="number" name="cost_price" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0" required>
                                                            </div>
                                                            <div class="form-text">Product cost price (excluding GST)</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Base Price</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">₹</span>
                                                                <input type="number" name="base_price" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0">
                                                            </div>
                                                            <div class="form-text">Base price for GST calculation (defaults to cost price)</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Selling Price</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">₹</span>
                                                                <input type="number" name="sell_price" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0">
                                                            </div>
                                                            <div class="form-text">Manual selling price (optional)</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">MRP</label>
                                                        <div class="col-lg-8">
                                                            <div class="input-group">
                                                                <span class="input-group-text">₹</span>
                                                                <input type="number" name="mrp" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0">
                                                            </div>
                                                            <div class="form-text">Maximum Retail Price</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">HSN/SAC Code</label>
                                                        <div class="col-lg-8">
                                                            <input type="text" name="hsn_code" class="form-control form-control-lg form-control-solid" placeholder="Enter HSN/SAC code">
                                                            <div class="form-text">Harmonized System of Nomenclature code</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Quantity</label>
                                                        <div class="col-lg-8">
                                                            <div class="row">
                                                                <div class="col-8">
                                                                    <input type="number" name="quantity" class="form-control form-control-lg form-control-solid" placeholder="0" min="0" required>
                                                                </div>
                                                                <div class="col-4">
                                                                    <select name="unit_of_measurement" class="form-select form-select-lg form-select-solid" required>
                                                                        <option value="PCS">PCS</option>
                                                                        <option value="KG">KG</option>
                                                                        <option value="LITER">LITER</option>
                                                                        <option value="METER">METER</option>
                                                                        <option value="BOX">BOX</option>
                                                                        <option value="DOZEN">DOZEN</option>
                                                                        <option value="GRAM">GRAM</option>
                                                                        <option value="ML">ML</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="form-text">Available stock quantity and unit</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Weight (KG)</label>
                                                        <div class="col-lg-8">
                                                            <input type="number" name="weight" class="form-control form-control-lg form-control-solid" placeholder="0.000" step="0.001" min="0">
                                                            <div class="form-text">Product weight in kilograms</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- GST & Tax Information -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">GST & Tax Information</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-6">
                                                        <div class="col-lg-6">
                                                            <label class="form-check form-check-custom form-check-solid">
                                                                <input class="form-check-input" type="checkbox" name="is_taxable" value="1" checked>
                                                                <span class="form-check-label fw-semibold">
                                                                    Is Taxable Product
                                                                </span>
                                                            </label>
                                                        </div>
                                                        <div class="col-lg-6">
                                                            <select name="tax_type" class="form-select form-select-lg form-select-solid" required>
                                                                <option value="exclusive">Tax Exclusive</option>
                                                                <option value="inclusive">Tax Inclusive</option>
                                                            </select>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">GST Rate (%)</label>
                                                        <div class="col-lg-8">
                                                            <select name="gst_rate" class="form-select form-select-lg form-select-solid" required>
                                                                <option value="0">0% - Exempt</option>
                                                                <option value="5">5% - Essential Items</option>
                                                                <option value="12">12% - Standard Items</option>
                                                                <option value="18" selected>18% - Most Items</option>
                                                                <option value="28">28% - Luxury Items</option>
                                                            </select>
                                                            <div class="form-text">Select applicable GST rate</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <div class="col-lg-4">
                                                            <label class="form-label fw-semibold fs-6">CGST Rate (%)</label>
                                                            <input type="number" name="cgst_rate" class="form-control form-control-lg form-control-solid" placeholder="9.00" step="0.01" min="0" max="14">
                                                            <div class="form-text">Central GST</div>
                                                        </div>
                                                        <div class="col-lg-4">
                                                            <label class="form-label fw-semibold fs-6">SGST Rate (%)</label>
                                                            <input type="number" name="sgst_rate" class="form-control form-control-lg form-control-solid" placeholder="9.00" step="0.01" min="0" max="14">
                                                            <div class="form-text">State GST</div>
                                                        </div>
                                                        <div class="col-lg-4">
                                                            <label class="form-label fw-semibold fs-6">IGST Rate (%)</label>
                                                            <input type="number" name="igst_rate" class="form-control form-control-lg form-control-solid" placeholder="18.00" step="0.01" min="0" max="28">
                                                            <div class="form-text">Integrated GST</div>
                                                        </div>
                                                    </div>

                                                    <div class="row mb-6">
                                                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Cess Rate (%)</label>
                                                        <div class="col-lg-8">
                                                            <input type="number" name="cess_rate" class="form-control form-control-lg form-control-solid" placeholder="0.00" step="0.01" min="0" max="100">
                                                            <div class="form-text">Additional cess if applicable</div>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Right Column -->
                                        <div class="col-lg-4">
                                            <!-- Product Images -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Product Images</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-6">
                                                        <label class="form-label fw-semibold fs-6">Primary Image</label>
                                                        <input type="file" name="primary_image" class="form-control form-control-lg form-control-solid" accept="image/*">
                                                        <div class="form-text">Upload main product image (JPEG, PNG, JPG, GIF, WebP - Max: 5MB)</div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <label class="form-label fw-semibold fs-6">Gallery Images</label>
                                                        <input type="file" name="gallery_images[]" class="form-control form-control-lg form-control-solid" accept="image/*" multiple>
                                                        <div class="form-text">Upload additional product images (Multiple files allowed - Max: 5MB each)</div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Product Settings -->
                                            <div class="card mb-5">
                                                <div class="card-header">
                                                    <h3 class="card-title">Product Settings</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="mb-6">
                                                        <label class="form-label required fw-semibold fs-6">Status</label>
                                                        <select name="status" class="form-select form-select-lg form-select-solid" required>
                                                            <option value="draft">Draft</option>
                                                            <option value="published">Published</option>
                                                            <option value="archived">Archived</option>
                                                        </select>
                                                        <div class="invalid-feedback"></div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" name="is_featured" id="is_featured">
                                                            <label class="form-check-label fw-semibold fs-6" for="is_featured">
                                                                Featured Product
                                                            </label>
                                                        </div>
                                                        <div class="form-text">Mark as featured product</div>
                                                    </div>

                                                    <div class="mb-6">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input" type="checkbox" name="is_tracking" id="is_tracking" checked>
                                                            <label class="form-check-label fw-semibold fs-6" for="is_tracking">
                                                                Track Inventory
                                                            </label>
                                                        </div>
                                                        <div class="form-text">Enable inventory tracking</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Form Actions -->
                                    <div class="d-flex justify-content-end">
                                        <a href="<?php echo e(route('products.index')); ?>" class="btn btn-light me-3">Cancel</a>
                                        <button type="submit" id="submit-btn" class="btn btn-primary">
                                            <span class="indicator-label">Create Product</span>
                                            <span class="indicator-progress">Please wait...
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with AJAX submission
            initializeEnhancedForm({
                formId: 'product-form',
                submitBtnId: 'submit-btn',
                successMessage: 'Product has been created successfully!',
                redirectUrl: '<?php echo e(route("products.index")); ?>',
                hasFileUpload: true,
                enableCKEditor: false,
                exitSelector: 'a[href="<?php echo e(route("products.index")); ?>"]'
            });

            // Image preview functionality with size validation
            $('input[name="primary_image"]').on('change', function() {
                const file = this.files[0];
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                if (file) {
                    // Check file size
                    if (file.size > maxSize) {
                        alert('Primary image size must be less than 5MB. Selected file is ' + (file.size / 1024 / 1024).toFixed(2) + 'MB');
                        $(this).val('');
                        return;
                    }

                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Remove existing preview
                        $(this).closest('.mb-6').find('.image-preview').remove();

                        // Add new preview
                        const preview = `
                            <div class="image-preview mt-3">
                                <img src="${e.target.result}" alt="Primary Image Preview" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                                <div class="mt-2">
                                    <small class="text-muted">Primary Image Preview (${(file.size / 1024 / 1024).toFixed(2)}MB)</small>
                                </div>
                            </div>
                        `;
                        $(this).closest('.mb-6').append(preview);
                    }.bind(this);
                    reader.readAsDataURL(file);
                }
            });

            // Gallery images preview with size validation
            $('input[name="gallery_images[]"]').on('change', function() {
                const files = this.files;
                const container = $(this).closest('.mb-6');
                const maxSize = 5 * 1024 * 1024; // 5MB in bytes

                // Remove existing previews
                container.find('.gallery-preview').remove();

                // Validate file sizes
                let validFiles = [];
                let invalidFiles = [];

                Array.from(files).forEach((file, index) => {
                    if (file.size > maxSize) {
                        invalidFiles.push(`Image ${index + 1}: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
                    } else {
                        validFiles.push(file);
                    }
                });

                if (invalidFiles.length > 0) {
                    alert('The following gallery images exceed 5MB limit:\n' + invalidFiles.join('\n') + '\n\nPlease select smaller images.');
                    $(this).val('');
                    return;
                }

                if (validFiles.length > 0) {
                    const previewContainer = $('<div class="gallery-preview mt-3"></div>');

                    validFiles.forEach((file, index) => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const preview = `
                                <div class="d-inline-block me-2 mb-2">
                                    <img src="${e.target.result}" alt="Gallery Image ${index + 1}" class="img-thumbnail" style="width: 100px; height: 100px; object-fit: cover;">
                                    <div class="text-center">
                                        <small class="text-muted">Image ${index + 1} (${(file.size / 1024 / 1024).toFixed(2)}MB)</small>
                                    </div>
                                </div>
                            `;
                            previewContainer.append(preview);
                        };
                        reader.readAsDataURL(file);
                    });

                    container.append(previewContainer);
                }
            });

            // Optional: Show preview of what SKU might look like
            $('input[name="name"]').on('input', function() {
                const name = $(this).val();
                const skuField = $('input[name="sku"]');

                if (name && !skuField.val()) {
                    const preview = name.toUpperCase()
                        .replace(/[^A-Z0-9]/g, '')
                        .substring(0, 6) + '-XXXX';
                    skuField.attr('placeholder', 'Auto-generated: ' + preview + ' (example)');
                } else if (!name) {
                    skuField.attr('placeholder', 'Auto-generated if empty');
                }
            });

            // Auto-calculate GST rates
            $('select[name="gst_rate"]').on('change', function() {
                const gstRate = parseFloat($(this).val()) || 0;
                const cgstRate = gstRate / 2;
                const sgstRate = gstRate / 2;

                $('input[name="cgst_rate"]').val(cgstRate.toFixed(2));
                $('input[name="sgst_rate"]').val(sgstRate.toFixed(2));
                $('input[name="igst_rate"]').val(gstRate.toFixed(2));
            });

            // Auto-fill base price from cost price if empty
            $('input[name="cost_price"]').on('blur', function() {
                const costPrice = $(this).val();
                const basePriceField = $('input[name="base_price"]');

                if (costPrice && !basePriceField.val()) {
                    basePriceField.val(costPrice);
                }
            });

            // Toggle tax fields based on is_taxable checkbox
            $('input[name="is_taxable"]').on('change', function() {
                const isTaxable = $(this).is(':checked');
                const taxFields = $('select[name="gst_rate"], input[name="cgst_rate"], input[name="sgst_rate"], input[name="igst_rate"], input[name="cess_rate"], select[name="tax_type"]');

                if (isTaxable) {
                    taxFields.prop('disabled', false).closest('.row').show();
                } else {
                    taxFields.prop('disabled', true).closest('.row').hide();
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Web/Products/Products/create.blade.php ENDPATH**/ ?>