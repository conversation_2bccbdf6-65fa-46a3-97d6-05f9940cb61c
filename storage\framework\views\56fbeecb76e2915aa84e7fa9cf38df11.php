<?php $__env->startSection('title', 'Product Details'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Product Details: <?php echo e($product->name); ?>

                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">View complete product information</span>
                                </h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('products.index')); ?>" class="btn btn-secondary me-2">
                                    <i class="fas fa-arrow-left"></i> Back to Products
                                </a>
                                <a href="<?php echo e(route('products.edit', $product->id)); ?>" class="btn btn-warning">
                                    <i class="fas fa-edit"></i> Edit Product
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row g-5 g-xl-10">
                    <!-- Left Column - Product Images -->
                    <div class="col-lg-5">
                        <div class="card card-flush">
                            <div class="card-header">
                                <h3 class="card-title">Product Images</h3>
                            </div>
                            <div class="card-body">
                                <?php if($product->primary_image): ?>
                                    <div class="mb-5">
                                        <label class="form-label fw-semibold fs-6 mb-3">Primary Image</label>
                                        <div class="text-center">
                                            <img src="<?php echo e(asset('uploads/products/' . $product->primary_image)); ?>"
                                                 alt="<?php echo e($product->name); ?>"
                                                 class="img-fluid rounded shadow-sm"
                                                 style="max-height: 400px; cursor: pointer;"
                                                 onclick="openImageModal(this.src)">
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if($product->gallery_images): ?>
                                    <?php
                                        $galleryImages = json_decode($product->gallery_images, true);
                                    ?>
                                    <?php if(!empty($galleryImages)): ?>
                                        <div class="mb-5">
                                            <label class="form-label fw-semibold fs-6 mb-3">Gallery Images</label>
                                            <div class="row g-3">
                                                <?php $__currentLoopData = $galleryImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="col-4">
                                                        <img src="<?php echo e(asset('uploads/products/' . $image)); ?>"
                                                             alt="Gallery Image"
                                                             class="img-thumbnail w-100"
                                                             style="height: 120px; object-fit: cover; cursor: pointer;"
                                                             onclick="openImageModal(this.src)">
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php if(!$product->primary_image && !$product->gallery_images): ?>
                                    <div class="text-center py-5">
                                        <i class="fas fa-image text-muted" style="font-size: 4rem;"></i>
                                        <p class="text-muted mt-3">No images available for this product</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Product Details -->
                    <div class="col-lg-7">
                        <!-- Basic Information -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Basic Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Product Name:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold"><?php echo e($product->name); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">SKU:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge badge-light-primary"><?php echo e($product->sku); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Category:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="badge badge-light-info"><?php echo e($product->category ? $product->category->name : 'N/A'); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Status:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php switch($product->status):
                                            case ('published'): ?>
                                                <span class="badge badge-success">Published</span>
                                                <?php break; ?>
                                            <?php case ('draft'): ?>
                                                <span class="badge badge-warning">Draft</span>
                                                <?php break; ?>
                                            <?php case ('archived'): ?>
                                                <span class="badge badge-secondary">Archived</span>
                                                <?php break; ?>
                                            <?php default: ?>
                                                <span class="badge badge-secondary">Unknown</span>
                                        <?php endswitch; ?>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Description:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <div class="text-gray-800"><?php echo e($product->description); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing & Inventory -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Pricing & Inventory</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Cost Price:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold text-success">$<?php echo e(number_format($product->cost_price, 2)); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Selling Price:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php if($product->sell_price): ?>
                                            <span class="fw-bold text-primary">$<?php echo e(number_format($product->sell_price, 2)); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Not for sale</span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Quantity:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="fw-bold <?php echo e($product->quantity > 0 ? 'text-success' : 'text-danger'); ?>">
                                            <?php echo e($product->quantity); ?> units
                                        </span>
                                        <?php if($product->quantity <= 5 && $product->quantity > 0): ?>
                                            <span class="badge badge-warning ms-2">Low Stock</span>
                                        <?php elseif($product->quantity == 0): ?>
                                            <span class="badge badge-danger ms-2">Out of Stock</span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <?php if($product->sell_price && $product->cost_price): ?>
                                    <?php
                                        $profit = $product->sell_price - $product->cost_price;
                                        $profitMargin = ($profit / $product->sell_price) * 100;
                                    ?>
                                    <div class="row mb-4">
                                        <div class="col-sm-4">
                                            <label class="fw-semibold text-muted">Profit Margin:</label>
                                        </div>
                                        <div class="col-sm-8">
                                            <span class="fw-bold <?php echo e($profit > 0 ? 'text-success' : 'text-danger'); ?>">
                                                $<?php echo e(number_format($profit, 2)); ?> (<?php echo e(number_format($profitMargin, 1)); ?>%)
                                            </span>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Product Settings -->
                        <div class="card card-flush mb-5">
                            <div class="card-header">
                                <h3 class="card-title">Product Settings</h3>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Featured Product:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php if($product->is_featured): ?>
                                            <span class="badge badge-success"><i class="fas fa-star"></i> Yes</span>
                                        <?php else: ?>
                                            <span class="badge badge-light-secondary">No</span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Inventory Tracking:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <?php if($product->is_tracking): ?>
                                            <span class="badge badge-success"><i class="fas fa-check"></i> Enabled</span>
                                        <?php else: ?>
                                            <span class="badge badge-light-secondary">Disabled</span>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Created:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted"><?php echo e($product->created_at->format('M d, Y \a\t h:i A')); ?></span>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-sm-4">
                                        <label class="fw-semibold text-muted">Last Updated:</label>
                                    </div>
                                    <div class="col-sm-8">
                                        <span class="text-muted"><?php echo e($product->updated_at->format('M d, Y \a\t h:i A')); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Product Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Product Image" class="img-fluid">
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        function openImageModal(imageSrc) {
            $('#modalImage').attr('src', imageSrc);
            $('#imageModal').modal('show');
        }

        $(document).ready(function() {
            // Initialize tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Web/Products/Products/show.blade.php ENDPATH**/ ?>