

<?php $__env->startSection('title', 'Arklok Admin | Edit Product Category'); ?>

<?php $__env->startSection('content'); ?>
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-fluid">
                <!-- Page Header -->
                <div class="row mt-5 mb-5">
                    <div class="col-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">
                                    Edit Product Category
                                    <span class="page-desc text-muted fs-7 fw-semibold pt-1">Update product category details</span>
                                </h1>
                            </div>
                            <div>
                                <a href="<?php echo e(route('product-categories.index')); ?>" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to Categories
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="row g-5 g-xl-10">
                    <div class="col-xl-12">
                        <div class="card card-flush">
                            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                                <div class="card-title">
                                    <h3 class="card-title align-items-start flex-column">
                                        <span class="card-label fw-bold fs-3 mb-1">Product Category Information</span>
                                        <span class="text-muted fw-semibold fs-7">Update the details below</span>
                                    </h3>
                                </div>
                            </div>
                            <div class="card-body pt-0">
                                <form id="product-category-form" action="<?php echo e(route('product-categories.update', $productCategory->id)); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PUT'); ?>
                                    <div class="row mb-6">
                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Category Name</label>
                                        <div class="col-lg-8">
                                            <input type="text" name="name" class="form-control form-control-lg form-control-solid"
                                                   placeholder="Enter category name" value="<?php echo e(old('name', $productCategory->name)); ?>" required />
                                            <div class="invalid-feedback"></div>
                                        </div>
                                    </div>

                                    <div class="row mb-6">
                                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Status</label>
                                        <div class="col-lg-8">
                                            <select name="status" class="form-select form-select-lg form-select-solid" required>
                                                <option value="">Select Status</option>
                                                <option value="1" <?php echo e(old('status', $productCategory->status) == '1' ? 'selected' : ''); ?>>Active</option>
                                                <option value="0" <?php echo e(old('status', $productCategory->status) == '0' ? 'selected' : ''); ?>>Inactive</option>
                                            </select>
                                            <div class="invalid-feedback"></div>
                                        </div>
                                    </div>

                                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                                        <a href="<?php echo e(route('product-categories.index')); ?>" class="btn btn-light btn-active-light-primary me-2">Cancel</a>
                                        <button type="submit" id="submit-btn" class="btn btn-primary">
                                            <span class="indicator-label">Update Category</span>
                                            <span class="indicator-progress">Please wait...
                                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                            </span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <script>
        $(document).ready(function() {
            // Initialize enhanced form with AJAX submission
            initializeEnhancedForm({
                formId: 'product-category-form',
                submitBtnId: 'submit-btn',
                successMessage: 'Product category has been updated successfully!',
                redirectUrl: '<?php echo e(route("product-categories.index")); ?>',
                hasFileUpload: false,
                enableCKEditor: false,
                exitSelector: 'a[href="<?php echo e(route("product-categories.index")); ?>"]'
            });
        });
    </script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('Layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xamp8.2\htdocs\abhishek_work\arclok_admin\resources\views/Web/Products/Product-Category/edit.blade.php ENDPATH**/ ?>